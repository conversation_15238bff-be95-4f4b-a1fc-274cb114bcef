Starting batch import at 2025-07-08T20:41:56.687Z
Environment variables loaded
Parameters:
- DBF file path: R:\Rpro\BRIDGE\invdb.dbf
- Target table: imported_table_rpro
- Truncate before import: true
- Batch size: 1000
- Start batch: 0
- End batch: 999999
DBF file exists
Supabase credentials found
- URL: https://aepabhlwpjfjulrjeitn.supabase.co
- Key: eyJhbGciOi...
Supabase client created
Opening DBF file...
DBF file opened. Found 10250 records.
Truncating table imported_table_rpro...
Table truncated successfully
Reading all records from DBF file...
Read 10250 records from DBF file.
Total number of batches: 11
Processing batches 1 to 11
Processing batch 1 (records 1-1000)
Processed 1000 records in batch 1
Inserting batch 1 into imported_table_rpro...
Successfully inserted batch 1 (1000 records)
Processing batch 2 (records 1001-2000)
Processed 1000 records in batch 2
Inserting batch 2 into imported_table_rpro...
Successfully inserted batch 2 (1000 records)
Processing batch 3 (records 2001-3000)
Processed 1000 records in batch 3
Inserting batch 3 into imported_table_rpro...
Successfully inserted batch 3 (1000 records)
Processing batch 4 (records 3001-4000)
Processed 1000 records in batch 4
Inserting batch 4 into imported_table_rpro...
Successfully inserted batch 4 (1000 records)
Processing batch 5 (records 4001-5000)
Processed 1000 records in batch 5
Inserting batch 5 into imported_table_rpro...
Successfully inserted batch 5 (1000 records)
Processing batch 6 (records 5001-6000)
Processed 1000 records in batch 6
Inserting batch 6 into imported_table_rpro...
Successfully inserted batch 6 (1000 records)
Processing batch 7 (records 6001-7000)
Processed 1000 records in batch 7
Inserting batch 7 into imported_table_rpro...
Successfully inserted batch 7 (1000 records)
Processing batch 8 (records 7001-8000)
Processed 1000 records in batch 8
Inserting batch 8 into imported_table_rpro...
Successfully inserted batch 8 (1000 records)
Processing batch 9 (records 8001-9000)
Processed 1000 records in batch 9
Inserting batch 9 into imported_table_rpro...
Successfully inserted batch 9 (1000 records)
Processing batch 10 (records 9001-10000)
Processed 1000 records in batch 10
Inserting batch 10 into imported_table_rpro...
Successfully inserted batch 10 (1000 records)
Processing batch 11 (records 10001-10250)
Processed 250 records in batch 11
Inserting batch 11 into imported_table_rpro...
Successfully inserted batch 11 (250 records)
Import completed. Total records imported: 10250
Batch import process completed at 2025-07-08T20:42:10.113Z
